import requests
import json
import tiktoken
import time
import random

# Initialize tiktoken encoder for token counting
enc = tiktoken.get_encoding("cl100k_base")
assert enc.decode(enc.encode("hello world")) == "hello world"

# Local LLM endpoints configuration
ENDPOINTS = [
    {'url': 'http://*************:4033/v1', 'api_key': 'your-api-key-here'},
    {'url': 'http://*************:1234/v1'},
    {'url': 'http://localhost:11434/v1'}
]

# Available models per endpoint
ENDPOINT_MODELS = {
    'http://*************:4033/v1': ["qwen3-30b-a3b-mlx", "qwen3-235b-a22b", "gemma-3-27b"],
    'http://*************:1234/v1': ["jan-nano"],
    'http://localhost:11434/v1': ["qwen3:0.6b"]
}

# Model to endpoint mapping for easy lookup
MODEL_TO_ENDPOINT = {}
for endpoint_url, models in ENDPOINT_MODELS.items():
    for model in models:
        MODEL_TO_ENDPOINT[model] = endpoint_url

def get_endpoint_for_model(model_name):
    """Get the endpoint configuration for a given model name"""
    endpoint_url = MODEL_TO_ENDPOINT.get(model_name)
    if endpoint_url:
        for endpoint in ENDPOINTS:
            if endpoint['url'] == endpoint_url:
                return endpoint
    return None

def call_local_llm(endpoint_url, model_name, messages, temperature=0.0, max_retries=3):
    """Call local LLM endpoint with retry logic"""
    headers = {
        'Content-Type': 'application/json'
    }

    data = {
        'model': model_name,
        'messages': messages,
        'temperature': temperature,
        'max_tokens': 2048,
        'stream': False
    }

    for attempt in range(max_retries):
        try:
            response = requests.post(
                f"{endpoint_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
                else:
                    print(f"Warning: Unexpected response format from {endpoint_url}")
                    return "Error: Invalid response format"
            else:
                print(f"HTTP Error {response.status_code} from {endpoint_url}: {response.text}")

        except requests.exceptions.Timeout:
            print(f"Timeout error for {endpoint_url} (attempt {attempt + 1}/{max_retries})")
        except requests.exceptions.ConnectionError:
            print(f"Connection error for {endpoint_url} (attempt {attempt + 1}/{max_retries})")
        except Exception as e:
            print(f"Unexpected error for {endpoint_url}: {str(e)} (attempt {attempt + 1}/{max_retries})")

        if attempt < max_retries - 1:
            wait_time = (attempt + 1) * 10  # Progressive backoff
            print(f"Waiting {wait_time} seconds before retry...")
            time.sleep(wait_time)

    return "Error: Failed to get response after all retries"

def GPT_response(messages, model_name):
    """Main function to get LLM response - updated for local endpoints"""
    token_num_count = 0

    # Count input tokens
    for item in messages:
        token_num_count += len(enc.encode(item["content"]))

    # Get endpoint for the model
    endpoint_url = get_endpoint_for_model(model_name)

    if endpoint_url is None:
        # If model not found, try to use a default model from the first available endpoint
        print(f"Warning: Model '{model_name}' not found. Using default model.")
        endpoint_url = ENDPOINTS[0]['url']
        model_name = ENDPOINT_MODELS[endpoint_url][0]  # Use first available model

    print(f'-------------------Model name: {model_name} on {endpoint_url}-------------------')

    # Call the local LLM
    response_content = call_local_llm(endpoint_url, model_name, messages)

    if response_content.startswith("Error:"):
        return 'Out of tokens', token_num_count

    # Count output tokens
    token_num_count += len(enc.encode(response_content))
    print(f'Token_num_count: {token_num_count}')

    return response_content, token_num_count
